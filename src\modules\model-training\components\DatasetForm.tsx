import React, { useState } from 'react';
import { Card, Input, Button, FormItem, Textarea } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { CreateDatasetSchema } from '../schemas/dataset.schema';
import {
  CreateDatasetDto,
  DatasetConversation,
  ImportedConversation,
} from '../types/dataset.types';
import { useCreateDataset } from '../hooks/useDatasetQuery';
import ChatLayout from './ChatLayout';

interface DatasetFormProps {
  /**
   * Callback khi tạo dataset thành công
   */
  onSuccess?: () => void;
}

/**
 * Component form tạo dataset với ChatLayout
 */
const DatasetForm: React.FC<DatasetFormProps> = ({ onSuccess }) => {
  const { t } = useTranslation();
  const createDataset = useCreateDataset();

  // State cho conversations và dataset
  const [conversations, setConversations] = useState<ImportedConversation[]>([]);
  const [showForm, setShowForm] = useState(false);

  // Khởi tạo form với React Hook Form và Zod
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<CreateDatasetDto>({
    resolver: zodResolver(CreateDatasetSchema),
    defaultValues: {
      name: '',
      description: '',
      trainData: [{ messages: [] }],
      validationData: [{ messages: [] }],
    },
  });

  // Handle conversations change từ ChatLayout
  const handleConversationsChange = (updatedConversations: ImportedConversation[]) => {
    setConversations(updatedConversations);
  };

  // Convert conversations to train/validation data for submission
  const convertConversationsToDataset = () => {
    if (conversations.length === 0) {
      return {
        trainData: [{ messages: [] }],
        validationData: [{ messages: [] }],
      };
    }

    // Convert ImportedConversation to DatasetConversation
    const datasetConversations: DatasetConversation[] = conversations.map(conv => ({
      messages: conv.messages,
    }));

    // Split 80% train, 20% validation
    const splitIndex = Math.floor(datasetConversations.length * 0.8);
    const trainData = datasetConversations.slice(0, splitIndex);
    const validationData = datasetConversations.slice(splitIndex);

    return {
      trainData: trainData.length > 0 ? trainData : [{ messages: [] }],
      validationData: validationData.length > 0 ? validationData : [{ messages: [] }],
    };
  };

  // Xử lý submit form
  const onSubmit = (data: CreateDatasetDto) => {
    const { trainData, validationData } = convertConversationsToDataset();

    // Gán dữ liệu train và validation vào form data
    const formData = {
      ...data,
      trainData,
      validationData,
    };

    createDataset.mutate(formData, {
      onSuccess: () => {
        reset();
        setConversations([]);
        if (onSuccess) {
          onSuccess();
        }
      },
    });
  };

  return (
    <div>
      {/* Form Header */}
      {showForm && (
        <Card>
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="grid grid-cols-1 gap-4 mb-4">
              <FormItem name="name" label={t('Name')} helpText={errors.name?.message} required>
                <Input
                  {...register('name')}
                  placeholder={t('Nhập tên dataset')}
                  error={errors.name?.message as string}
                  fullWidth
                />
              </FormItem>

              <FormItem
                name="description"
                label={t('Description')}
                helpText={errors.description?.message}
                required
              >
                <Textarea
                  {...register('description')}
                  placeholder={t('Nhập mô tả dataset')}
                  status={errors.description?.message ? 'error' : 'default'}
                  rows={3}
                  fullWidth
                />
              </FormItem>
            </div>

            <div className="flex justify-between items-center">
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {conversations.length > 0 && (
                  <span>
                    {conversations.length} conversations •{Math.floor(conversations.length * 0.8)}{' '}
                    train, {Math.ceil(conversations.length * 0.2)} validation
                  </span>
                )}
              </div>

              <div className="flex space-x-2 ">
                <Button type="button" variant="outline" onClick={() => setShowForm(false)}>
                  {t('Cancel')}
                </Button>
                <Button
                  type="submit"
                  isLoading={createDataset.isPending}
                  disabled={conversations.length === 0}
                >
                  {t('Tạo Dataset')}
                </Button>
              </div>
            </div>
          </form>
        </Card>
      )}

      {/* Chat Layout Container */}
      <div className="relative">
        <div className="h-[600px] ">
          <ChatLayout onConversationsChange={handleConversationsChange} />
        </div>

        {/* Create Dataset Button */}
        {conversations.length > 0 && !showForm && (
          <div className="absolute top-8 right-4 ">
            <Button onClick={() => setShowForm(true)} size="sm">
              {t('Tạo Dataset')} ({conversations.length})
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default DatasetForm;
