import React, { useState } from 'react';
import { Card, Input, Button, FormItem, Textarea } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { CreateDatasetSchema } from '../schemas/dataset.schema';
import {
  CreateDatasetDto,
  DatasetConversation,
  ImportedConversation,
} from '../types/dataset.types';
import { useCreateDataset } from '../hooks/useDatasetQuery';
import ChatLayout from './ChatLayout';

interface ValidationDataFormProps {
  /**
   * Callback khi tạo validation dataset thành công
   */
  onSuccess?: () => void;

  /**
   * Dữ liệu training để tham khảo (optional)
   */
  trainingData?: ImportedConversation[];

  /**
   * Callback để sync dữ liệu với training data
   */
  onSyncWithTraining?: (conversations: ImportedConversation[]) => void;
}

/**
 * Component form tạo validation dataset với ChatLayout
 */
const ValidationDataForm: React.FC<ValidationDataFormProps> = ({ 
  onSuccess, 
  trainingData = [],
  onSyncWithTraining 
}) => {
  const { t } = useTranslation();
  const createDataset = useCreateDataset();

  // State cho conversations và dataset
  const [conversations, setConversations] = useState<ImportedConversation[]>([]);
  const [showForm, setShowForm] = useState(false);

  // Khởi tạo form với React Hook Form và Zod
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<CreateDatasetDto>({
    resolver: zodResolver(CreateDatasetSchema),
    defaultValues: {
      name: '',
      description: '',
      trainData: [{ messages: [] }],
      validationData: [{ messages: [] }],
    },
  });

  // Handle conversations change từ ChatLayout
  const handleConversationsChange = (updatedConversations: ImportedConversation[]) => {
    setConversations(updatedConversations);
    // Notify parent component if needed
    if (onSyncWithTraining) {
      onSyncWithTraining(updatedConversations);
    }
  };

  // Convert conversations to validation data for submission
  const convertConversationsToValidationDataset = () => {
    if (conversations.length === 0) {
      return {
        trainData: [{ messages: [] }],
        validationData: [{ messages: [] }],
      };
    }

    // Convert ImportedConversation to DatasetConversation
    const datasetConversations: DatasetConversation[] = conversations.map(conv => ({
      messages: conv.messages,
    }));

    // Tất cả conversations sẽ là validation data
    return {
      trainData: [{ messages: [] }], // Empty train data for validation-only dataset
      validationData: datasetConversations,
    };
  };

  // Import từ training data
  const handleImportFromTraining = () => {
    if (trainingData.length > 0) {
      // Lấy 20% cuối của training data làm validation data
      const validationCount = Math.ceil(trainingData.length * 0.2);
      const validationConversations = trainingData.slice(-validationCount).map(conv => ({
        ...conv,
        id: `val_${conv.id}`, // Prefix để phân biệt
        title: `[Validation] ${conv.title}`,
      }));
      
      setConversations(prev => [...prev, ...validationConversations]);
    }
  };

  // Xử lý submit form
  const onSubmit = (data: CreateDatasetDto) => {
    const { trainData, validationData } = convertConversationsToValidationDataset();

    // Gán dữ liệu validation vào form data
    const formData = {
      ...data,
      name: `${data.name} (Validation)`,
      trainData,
      validationData,
    };

    createDataset.mutate(formData, {
      onSuccess: () => {
        reset();
        setConversations([]);
        if (onSuccess) {
          onSuccess();
        }
      },
    });
  };

  return (
    <div className="space-y-4">
      {/* Form Header */}
      {showForm && (
        <Card>
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="grid grid-cols-1 gap-4 mb-4">
              <FormItem name="name" label={t('Name')} helpText={errors.name?.message} required>
                <Input
                  {...register('name')}
                  placeholder={t('Nhập tên validation dataset')}
                  error={errors.name?.message as string}
                  fullWidth
                />
              </FormItem>

              <FormItem
                name="description"
                label={t('Description')}
                helpText={errors.description?.message}
                required
              >
                <Textarea
                  {...register('description')}
                  placeholder={t('Nhập mô tả validation dataset')}
                  status={errors.description?.message ? 'error' : 'default'}
                  rows={3}
                  fullWidth
                />
              </FormItem>
            </div>

            <div className="flex justify-between items-center">
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {conversations.length > 0 && (
                  <span>
                    {conversations.length} validation conversations
                  </span>
                )}
              </div>

              <div className="flex space-x-2">
                <Button type="button" variant="outline" onClick={() => setShowForm(false)}>
                  {t('Cancel')}
                </Button>
                <Button
                  type="submit"
                  isLoading={createDataset.isPending}
                  disabled={conversations.length === 0}
                >
                  {t('Tạo Validation Dataset')}
                </Button>
              </div>
            </div>
          </form>
        </Card>
      )}

      {/* Chat Layout Container */}
      <Card className="relative">
        <div className="h-[600px]">
          <ChatLayout onConversationsChange={handleConversationsChange} />
        </div>

        {/* Action Buttons */}
        <div className="absolute top-4 right-4 flex gap-2">
          {/* Import from Training Button */}
          {trainingData.length > 0 && (
            <Button 
              onClick={handleImportFromTraining} 
              size="sm" 
              variant="outline"
            >
              {t('Import từ Training')} ({Math.ceil(trainingData.length * 0.2)})
            </Button>
          )}

          {/* Create Dataset Button */}
          {conversations.length > 0 && !showForm && (
            <Button onClick={() => setShowForm(true)} size="sm">
              {t('Tạo Validation Dataset')} ({conversations.length})
            </Button>
          )}
        </div>
      </Card>
    </div>
  );
};

export default ValidationDataForm;
