import React from 'react';
import { ValidationDataForm } from '../components';
import { useNavigate } from 'react-router-dom';

/**
 * Trang tạo validation dataset
 */
const CreateValidationDataPage: React.FC = () => {
  const navigate = useNavigate();

  const handleSuccess = () => {
    // Chuyển hướng đến trang danh sách dataset sau khi tạo thành công
    navigate('/model-training/datasets');
  };

  return (
    <div>
      <ValidationDataForm onSuccess={handleSuccess} />
    </div>
  );
};

export default CreateValidationDataPage;
