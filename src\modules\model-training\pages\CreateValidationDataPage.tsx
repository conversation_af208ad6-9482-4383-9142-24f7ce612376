import React, { useState } from 'react';
import { Card, Typography } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { ChatLayout } from '../components';
import { ImportedConversation } from '../types/dataset.types';
import { BarChart3 } from 'lucide-react';

/**
 * Trang tạo validation dataset - tương tự training data
 */
const CreateValidationDataPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [conversations, setConversations] = useState<ImportedConversation[]>([]);

  const handleSuccess = () => {
    // Chuyển hướng đến trang danh sách dataset sau khi tạo thành công
    navigate('/model-training/datasets');
  };

  // Handle conversations change từ ChatLayout
  const handleConversationsChange = (updatedConversations: ImportedConversation[]) => {
    setConversations(updatedConversations);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <Typography variant="h4" className="flex items-center">
            <BarChart3 className="mr-3 text-green-600" size={28} />
            {t('Validation Dataset Creation')}
          </Typography>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            {t('Tạo và quản lý dữ liệu validation cho fine-tuning model')}
          </p>
        </div>

        {/* Statistics */}
        {conversations.length > 0 && (
          <Card className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{conversations.length}</div>
              <div className="text-sm text-gray-500">Validation Conversations</div>
            </div>
          </Card>
        )}
      </div>

      {/* Chat Layout Container - giống y như training data */}
      <Card>
        <div className="h-[600px]">
          <ChatLayout onConversationsChange={handleConversationsChange} />
        </div>
      </Card>

      {/* Tips */}
      <Card className="p-4 bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800">
        <Typography variant="h6" className="text-green-800 dark:text-green-200 mb-2">
          💡 {t('Validation Data Tips')}
        </Typography>
        <ul className="text-sm text-green-700 dark:text-green-300 space-y-1">
          <li>• {t('Validation data được sử dụng để đánh giá hiệu suất model')}</li>
          <li>• {t('Không nên trùng với training data')}</li>
          <li>• {t('Khuyến nghị: 20% tổng dữ liệu')}</li>
          <li>• {t('Import JSONL hoặc tạo conversations mới')}</li>
        </ul>
      </Card>
    </div>
  );
};

export default CreateValidationDataPage;
