import React, { useState } from 'react';
import { Card, Button, Typography } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { DatasetForm, ValidationDataForm } from '../components';
import { ImportedConversation } from '../types/dataset.types';
import { Database, FileText, BarChart3 } from 'lucide-react';

/**
 * Trang quản lý dataset tổng hợp (Training + Validation)
 */
const DatasetManagementPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  // State để chia sẻ dữ liệu giữa training và validation
  const [trainingConversations, setTrainingConversations] = useState<ImportedConversation[]>([]);
  const [validationConversations, setValidationConversations] = useState<ImportedConversation[]>(
    []
  );
  const [activeTab, setActiveTab] = useState('training');

  const handleSuccess = () => {
    // Chuyển hướng đến trang danh sách dataset sau khi tạo thành công
    navigate('/model-training/datasets');
  };

  // Handle training data changes
  const handleTrainingDataChange = (conversations: ImportedConversation[]) => {
    setTrainingConversations(conversations);
  };

  // Handle validation data changes
  const handleValidationDataChange = (conversations: ImportedConversation[]) => {
    setValidationConversations(conversations);
  };

  // Auto split training data to validation
  const handleAutoSplit = () => {
    if (trainingConversations.length > 0) {
      const splitIndex = Math.floor(trainingConversations.length * 0.8);
      const validationData = trainingConversations.slice(splitIndex).map(conv => ({
        ...conv,
        id: `val_${conv.id}`,
        title: `[Validation] ${conv.title}`,
      }));

      setValidationConversations(prev => [...prev, ...validationData]);
      setActiveTab('validation');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <Typography variant="h4" className="flex items-center">
            <Database className="mr-3 text-primary" size={28} />
            {t('Dataset Management')}
          </Typography>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            {t('Quản lý dữ liệu huấn luyện và validation cho fine-tuning model')}
          </p>
        </div>

        {/* Statistics */}
        <Card className="p-4">
          <div className="flex items-center space-x-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{trainingConversations.length}</div>
              <div className="text-sm text-gray-500">Training</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {validationConversations.length}
              </div>
              <div className="text-sm text-gray-500">Validation</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {trainingConversations.length + validationConversations.length}
              </div>
              <div className="text-sm text-gray-500">Total</div>
            </div>
          </div>
        </Card>
      </div>

      {/* Navigation */}
      <div className="flex items-center justify-between">
        {/* Button Group Navigation */}
        <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
          <Button
            variant={activeTab === 'training' ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('training')}
            className="flex items-center"
          >
            <FileText size={16} className="mr-2" />
            {t('Training Data')}
          </Button>
          <Button
            variant={activeTab === 'validation' ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('validation')}
            className="flex items-center"
          >
            <BarChart3 size={16} className="mr-2" />
            {t('Validation Data')}
          </Button>
        </div>

        {/* Quick Actions */}
        <div className="flex space-x-2">
          {trainingConversations.length > 0 && (
            <Button onClick={handleAutoSplit} variant="outline" size="sm">
              {t('Auto Split 80/20')}
            </Button>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="mt-6">
        {/* Training Data Content */}
        {activeTab === 'training' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Typography variant="h6" className="flex items-center">
                <FileText className="mr-2 text-blue-600" size={20} />
                {t('Training Dataset')}
              </Typography>
              <div className="text-sm text-gray-500">
                {trainingConversations.length > 0 && (
                  <span>
                    {trainingConversations.length} conversations • Recommended: 80% of total data
                  </span>
                )}
              </div>
            </div>

            <DatasetForm
              onSuccess={handleSuccess}
              onConversationsChange={handleTrainingDataChange}
            />
          </div>
        )}

        {/* Validation Data Content */}
        {activeTab === 'validation' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Typography variant="h6" className="flex items-center">
                <BarChart3 className="mr-2 text-green-600" size={20} />
                {t('Validation Dataset')}
              </Typography>
              <div className="text-sm text-gray-500">
                {validationConversations.length > 0 && (
                  <span>
                    {validationConversations.length} conversations • Recommended: 20% of total data
                  </span>
                )}
              </div>
            </div>

            <ValidationDataForm
              onSuccess={handleSuccess}
              trainingData={trainingConversations}
              onSyncWithTraining={handleValidationDataChange}
            />
          </div>
        )}
      </div>

      {/* Tips */}
      <Card className="p-4 bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
        <Typography variant="h6" className="text-blue-800 dark:text-blue-200 mb-2">
          💡 {t('Tips')}
        </Typography>
        <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
          <li>• {t('Training data được sử dụng để huấn luyện model')}</li>
          <li>• {t('Validation data được sử dụng để đánh giá hiệu suất model')}</li>
          <li>• {t('Tỷ lệ khuyến nghị: 80% training, 20% validation')}</li>
          <li>• {t('Đảm bảo dữ liệu validation không trùng với training')}</li>
        </ul>
      </Card>
    </div>
  );
};

export default DatasetManagementPage;
