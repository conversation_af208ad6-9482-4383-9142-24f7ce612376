import React, { useState } from 'react';
import { Button } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { DatasetForm, ValidationDataForm } from '../components';
import { ImportedConversation } from '../types/dataset.types';
import { FileText, BarChart3 } from 'lucide-react';

/**
 * Trang quản lý dataset tổng hợp (Training + Validation)
 */
const DatasetManagementPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  // State để chia sẻ dữ liệu giữa training và validation
  const [trainingConversations, setTrainingConversations] = useState<ImportedConversation[]>([]);
  const [validationConversations, setValidationConversations] = useState<ImportedConversation[]>(
    []
  );
  const [activeTab, setActiveTab] = useState('training');

  const handleSuccess = () => {
    // Chuyển hướng đến trang danh sách dataset sau khi tạo thành công
    navigate('/model-training/datasets');
  };

  // Handle training data changes
  const handleTrainingDataChange = (conversations: ImportedConversation[]) => {
    setTrainingConversations(conversations);
  };

  // Handle validation data changes
  const handleValidationDataChange = (conversations: ImportedConversation[]) => {
    setValidationConversations(conversations);
  };

  // Auto split training data to validation
  const handleAutoSplit = () => {
    if (trainingConversations.length > 0) {
      const splitIndex = Math.floor(trainingConversations.length * 0.8);
      const validationData = trainingConversations.slice(splitIndex).map(conv => ({
        ...conv,
        id: `val_${conv.id}`,
        title: `[Validation] ${conv.title}`,
      }));

      setValidationConversations(prev => [...prev, ...validationData]);
      setActiveTab('validation');
    }
  };

  return (
    <div className="space-y-6">
      {/* Navigation */}
      <div className="flex items-center justify-between">
        {/* Button Group Navigation */}
        <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
          <Button
            variant={activeTab === 'training' ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('training')}
            className="flex items-center"
          >
            <FileText size={16} className="mr-2" />
            {t('Training Data')}
          </Button>
          <Button
            variant={activeTab === 'validation' ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('validation')}
            className="flex items-center"
          >
            <BarChart3 size={16} className="mr-2" />
            {t('Validation Data')}
          </Button>
        </div>

        {/* Quick Actions */}
        <div className="flex space-x-2">
          {trainingConversations.length > 0 && (
            <Button onClick={handleAutoSplit} variant="outline" size="sm">
              {t('Auto Split 80/20')}
            </Button>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="mt-6">
        {/* Training Data Content */}
        {activeTab === 'training' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-500">
                {trainingConversations.length > 0 && (
                  <span>
                    {trainingConversations.length} conversations • Recommended: 80% of total data
                  </span>
                )}
              </div>
            </div>

            <DatasetForm
              onSuccess={handleSuccess}
              onConversationsChange={handleTrainingDataChange}
            />
          </div>
        )}

        {/* Validation Data Content */}
        {activeTab === 'validation' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-500">
                {validationConversations.length > 0 && (
                  <span>
                    {validationConversations.length} conversations • Recommended: 20% of total data
                  </span>
                )}
              </div>
            </div>

            <ValidationDataForm
              onSuccess={handleSuccess}
              trainingData={trainingConversations}
              onSyncWithTraining={handleValidationDataChange}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default DatasetManagementPage;
