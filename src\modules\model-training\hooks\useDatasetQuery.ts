import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { DATASET_QUERY_KEYS } from '../constants/dataset-query-key';
import {
  CreateDatasetDto,
  CreateFineTuneDatasetDto,
  QueryDatasetDto,
  ImportedConversation,
} from '../types/dataset.types';
import {
  createDataset,
  createFineTuneDataset,
  uploadJsonlData,
  getDatasets,
  getDatasetDetail,
  updateDataset,
  deleteDataset,
} from '../services/dataset.service';

/**
 * Hook để lấy danh sách dataset
 * @param queryDto Tham số truy vấn
 * @returns Query object
 */
export const useDatasets = (queryDto?: QueryDatasetDto) => {
  return useQuery({
    queryKey: DATASET_QUERY_KEYS.list(queryDto || {}),
    queryFn: () => getDatasets(queryDto),
  });
};

/**
 * Hook để lấy thông tin chi tiết dataset
 * @param id ID của dataset
 * @returns Query object
 */
export const useDatasetDetail = (id: string) => {
  return useQuery({
    queryKey: DATASET_QUERY_KEYS.detail(id),
    queryFn: () => getDatasetDetail(id),
  });
};

/**
 * Hook để tạo dataset mới
 * @returns Mutation object
 */
export const useCreateDataset = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateDatasetDto) => createDataset(data),
    onSuccess: () => {
      // Invalidate và refetch danh sách dataset
      queryClient.invalidateQueries({ queryKey: DATASET_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để cập nhật dataset
 * @param id ID của dataset
 * @returns Mutation object
 */
export const useUpdateDataset = (id: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: Partial<CreateDatasetDto>) => updateDataset(id, data),
    onSuccess: () => {
      // Invalidate và refetch danh sách dataset và chi tiết dataset
      queryClient.invalidateQueries({ queryKey: DATASET_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: DATASET_QUERY_KEYS.detail(id) });
    },
  });
};

/**
 * Hook để xóa dataset
 * @returns Mutation object
 */
export const useDeleteDataset = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deleteDataset(id),
    onSuccess: () => {
      // Invalidate và refetch danh sách dataset
      queryClient.invalidateQueries({ queryKey: DATASET_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để tạo fine-tune dataset mới
 * @returns Mutation object với helper functions
 */
export const useCreateFineTuneDataset = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateFineTuneDatasetDto) => createFineTuneDataset(data),
    onSuccess: () => {
      // Invalidate và refetch danh sách dataset
      queryClient.invalidateQueries({ queryKey: DATASET_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để upload JSONL data
 * @returns Mutation object
 */
export const useUploadJsonlData = () => {
  return useMutation({
    mutationFn: (data: { jsonlData: string; filename: string }) =>
      uploadJsonlData(data.jsonlData, data.filename),
  });
};

/**
 * Helper function để convert conversations thành JSONL
 * @param conversations Danh sách conversations
 * @returns JSONL string
 */
export const convertConversationsToJsonl = (conversations: ImportedConversation[]): string => {
  return conversations.map(conv => JSON.stringify({ messages: conv.messages })).join('\n');
};
