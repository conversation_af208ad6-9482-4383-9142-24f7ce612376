import { useQueryClient } from '@tanstack/react-query';
import { useApiQuery, useApiMutation, useApiDeleteMutation } from '@/shared/api/hooks';
import { DATASET_QUERY_KEYS } from '../constants/dataset-query-key';
import {
  CreateDatasetDto,
  CreateFineTuneDatasetDto,
  DatasetResponseDto,
  QueryDatasetDto,
  ImportedConversation,
} from '../types/dataset.types';
import { PaginatedResult } from '@/shared/dto/response/api-response.dto';

/**
 * Hook để lấy danh sách dataset
 * @param queryDto Tham số truy vấn
 * @returns Query object
 */
export const useDatasets = (queryDto?: QueryDatasetDto) => {
  return useApiQuery<PaginatedResult<DatasetResponseDto>>(
    DATASET_QUERY_KEYS.list(queryDto || {}),
    '/user/datasets',
    {
      params: queryDto,
    }
  );
};

/**
 * Hook để lấy thông tin chi tiết dataset
 * @param id ID của dataset
 * @returns Query object
 */
export const useDatasetDetail = (id: string) => {
  return useApiQuery<DatasetResponseDto>(DATASET_QUERY_KEYS.detail(id), `/user/datasets/${id}`);
};

/**
 * Hook để tạo dataset mới
 * @returns Mutation object
 */
export const useCreateDataset = () => {
  const queryClient = useQueryClient();

  return useApiMutation<DatasetResponseDto, CreateDatasetDto>('/user/datasets', {
    onSuccess: () => {
      // Invalidate và refetch danh sách dataset
      queryClient.invalidateQueries({ queryKey: DATASET_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để cập nhật dataset
 * @param id ID của dataset
 * @returns Mutation object
 */
export const useUpdateDataset = (id: string) => {
  const queryClient = useQueryClient();

  return useApiMutation<DatasetResponseDto, Partial<CreateDatasetDto>>(`/user/datasets/${id}`, {
    onSuccess: () => {
      // Invalidate và refetch danh sách dataset và chi tiết dataset
      queryClient.invalidateQueries({ queryKey: DATASET_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: DATASET_QUERY_KEYS.detail(id) });
    },
  });
};

/**
 * Hook để xóa dataset
 * @returns Mutation object
 */
export const useDeleteDataset = () => {
  const queryClient = useQueryClient();

  return useApiDeleteMutation<{ success: boolean }, string>('/user/datasets', {
    onSuccess: () => {
      // Invalidate và refetch danh sách dataset
      queryClient.invalidateQueries({ queryKey: DATASET_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để tạo fine-tune dataset mới
 * @returns Mutation object với helper functions
 */
export const useCreateFineTuneDataset = () => {
  const queryClient = useQueryClient();

  return useApiMutation<DatasetResponseDto, CreateFineTuneDatasetDto>('/user/fine-tune-datasets', {
    onSuccess: () => {
      // Invalidate và refetch danh sách dataset
      queryClient.invalidateQueries({ queryKey: DATASET_QUERY_KEYS.lists() });
    },
  });
};

/**
 * Hook để upload JSONL data
 * @returns Mutation object
 */
export const useUploadJsonlData = () => {
  return useApiMutation<{ filePath: string }, FormData>('/user/upload-dataset', {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

/**
 * Helper function để convert conversations thành JSONL
 * @param conversations Danh sách conversations
 * @returns JSONL string
 */
export const convertConversationsToJsonl = (conversations: ImportedConversation[]): string => {
  return conversations.map(conv => JSON.stringify({ messages: conv.messages })).join('\n');
};
